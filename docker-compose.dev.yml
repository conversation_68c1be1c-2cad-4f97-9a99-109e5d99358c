# Uru Workspace Platform - Development Docker Compose
# This configuration is optimized for local development with the new architecture
# Use: docker-compose -f docker-compose.dev.yml up --build

version: '3.8'

networks:
  uru-dev-network:
    driver: bridge

services:
  # ===========================================
  # AUTHENTICATION SERVICE (Port 8003)
  # ===========================================
  
  auth-service:
    build: 
      context: ./auth-service
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    networks:
      - uru-dev-network
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-dev-encryption-key}
      # Development URLs
      - FRONTEND_URL=http://localhost:3000
      - AUTH_SERVICE_URL=http://localhost:8003
      - INTEGRATIONS_SERVICE_URL=http://localhost:8002
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8001,http://localhost:8002,http://localhost:8003
      - APP_HOST=0.0.0.0
      - APP_PORT=8003
      - DEBUG=true
      # Environment detection
      - NODE_ENV=development
      - ENVIRONMENT=development
      - USE_NEW_ARCHITECTURE=true
    volumes:
      - ./auth-service:/app
      - ./shared:/app/shared
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # ===========================================
  # INTEGRATION SERVICE (Port 8002)
  # ===========================================
  
  integration-service:
    build: 
      context: ./integration-service
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    networks:
      - uru-dev-network
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      # Development OAuth redirect
      - GOOGLE_REDIRECT_URI=http://localhost:8002/oauth/google/callback
      - FRONTEND_URL=http://localhost:3000
      - AUTH_SERVICE_URL=http://localhost:8003
      - INTEGRATIONS_SERVICE_URL=http://localhost:8002
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8001,http://localhost:8002,http://localhost:8003
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - APP_HOST=0.0.0.0
      - APP_PORT=8002
      - DEBUG=true
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-dev-encryption-key}
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
      # Environment detection
      - NODE_ENV=development
      - ENVIRONMENT=development
      - USE_NEW_ARCHITECTURE=true
    volumes:
      - ./integration-service:/app
      - ./shared:/app/shared
    depends_on:
      auth-service:
        condition: service_healthy
      composio-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # ===========================================
  # COMPOSIO SERVICE (Port 8001)
  # ===========================================
  
  composio-service:
    build: 
      context: ./composio-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    networks:
      - uru-dev-network
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - URU_COMPOSIO_API_KEY=${URU_COMPOSIO_API_KEY}
      - URU_COMPOSIO_BASE_URL=${URU_COMPOSIO_BASE_URL:-https://backend.composio.dev/api}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-dev-encryption-key}
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8001,http://localhost:8002,http://localhost:8003
      - FRONTEND_URL=http://localhost:3000
      - AUTH_SERVICE_URL=http://localhost:8003
      - INTEGRATIONS_SERVICE_URL=http://localhost:8002
      - COMPOSIO_SERVICE_URL=http://localhost:8001
      # Google OAuth credentials for white-labeled flow
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      # Environment detection
      - NODE_ENV=development
      - ENVIRONMENT=development
      - USE_NEW_ARCHITECTURE=true
      - DEBUG=true
    volumes:
      - ./composio-service:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # ===========================================
  # MCP PROXY SERVICE (Port 3001)
  # ===========================================
  
  mcp-proxy:
    build: 
      context: ./mcp-proxy
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    networks:
      - uru-dev-network
    environment:
      - PORT=3001
      # Development environment
      - NODE_ENV=development
      - ENVIRONMENT=development
      - AUTH_SERVICE_URL=http://auth-service:8003
      - INTEGRATIONS_SERVICE_URL=http://integration-service:8002
      - MCP_PROXY_URL=http://localhost:3001
      - FRONTEND_URL=http://localhost:3000
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8001,http://localhost:8002,http://localhost:8003
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - N8N_SSE_URL=${N8N_SSE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-key}
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
      - USE_NEW_ARCHITECTURE=true
      - DEBUG=true
    volumes:
      - ./mcp-proxy:/app
      - ./shared:/app/shared
    depends_on:
      auth-service:
        condition: service_healthy
      integration-service:
        condition: service_healthy
      composio-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # ===========================================
  # FRONTEND SERVICE (Port 3000)
  # ===========================================
  
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    networks:
      - uru-dev-network
    environment:
      # Development environment
      - NODE_ENV=development
      - ENVIRONMENT=development
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - NEXT_PUBLIC_API_URL=http://localhost:3000
      - NEXT_PUBLIC_AUTH_URL=http://localhost:8003
      - NEXT_PUBLIC_INTEGRATIONS_URL=http://localhost:8002
      - NEXT_PUBLIC_MCP_URL=http://localhost:3001
      - NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true
      - FRONTEND_URL=http://localhost:3000
      - AUTH_SERVICE_URL=http://auth-service:8003
      - INTEGRATIONS_SERVICE_URL=http://integration-service:8002
      - USE_NEW_ARCHITECTURE=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      auth-service:
        condition: service_healthy
      integration-service:
        condition: service_healthy
      mcp-proxy:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
