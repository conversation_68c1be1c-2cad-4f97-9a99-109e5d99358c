# Uru Workspace Platform - Local Development Environment Configuration
# This file provides localhost defaults for local development
# Copy to .env for local development use

# ===========================================
# ENVIRONMENT DETECTION
# ===========================================
NODE_ENV=development
ENVIRONMENT=development
DEBUG=true

# ===========================================
# SUPABASE DATABASE CONFIGURATION
# ===========================================
# Replace with your actual Supabase credentials
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# ===========================================
# GOOGLE OAUTH CONFIGURATION
# ===========================================
# Replace with your actual Google OAuth credentials
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret

# Development redirect URI (localhost)
GOOGLE_REDIRECT_URI=http://localhost:8000/oauth/google/callback

# ===========================================
# JWT AND ENCRYPTION CONFIGURATION
# ===========================================
# Use secure values even for development
JWT_SECRET=dev-jwt-secret-change-this-in-production
ENCRYPTION_KEY=dev-encryption-key-change-this-in-production

# ===========================================
# SERVICE URLS (LOCALHOST DEFAULTS)
# ===========================================
# These will be automatically used for development
FRONTEND_URL=http://localhost:3000
AUTH_SERVICE_URL=http://localhost:8003
INTEGRATIONS_SERVICE_URL=http://localhost:8002
COMPOSIO_SERVICE_URL=http://localhost:8001
MCP_PROXY_URL=http://localhost:3001

# ===========================================
# FRONTEND CONFIGURATION
# ===========================================
# Next.js public environment variables for development
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_AUTH_URL=http://localhost:8003
NEXT_PUBLIC_INTEGRATIONS_URL=http://localhost:8002
NEXT_PUBLIC_MCP_URL=http://localhost:3001
NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true

# ===========================================
# N8N INTEGRATION
# ===========================================
# Replace with your actual n8n endpoint
N8N_SSE_URL=your-n8n-sse-endpoint-url

# ===========================================
# CORS CONFIGURATION (DEVELOPMENT)
# ===========================================
# Localhost origins for development - New Architecture
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8001,http://localhost:8002,http://localhost:8003,http://127.0.0.1:3000,http://127.0.0.1:3001,http://127.0.0.1:8001,http://127.0.0.1:8002,http://127.0.0.1:8003

# ===========================================
# OPTIONAL CONFIGURATION
# ===========================================
# OpenAI API key for AI features (if needed)
OPENAI_API_KEY=your-openai-api-key

# OAuth service configuration
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
APP_HOST=0.0.0.0
APP_PORT=8000

# ===========================================
# DEVELOPMENT SETUP INSTRUCTIONS
# ===========================================
# 1. Copy this file to .env in the root directory
# 2. Replace placeholder values with your actual credentials
# 3. Ensure Google OAuth app is configured with localhost redirect URI:
#    http://localhost:8000/oauth/google/callback
# 4. Run: docker-compose up
# 5. Access frontend at: http://localhost:3000

# ===========================================
# GOOGLE OAUTH SETUP FOR DEVELOPMENT
# ===========================================
# In Google Cloud Console, add these redirect URIs:
# - http://localhost:8000/oauth/google/callback (for development)
# - https://oauth.uruenterprises.com/oauth/google/callback (for production)
#
# Required scopes:
# - https://www.googleapis.com/auth/gmail.readonly
# - https://www.googleapis.com/auth/gmail.send
# - https://www.googleapis.com/auth/drive.readonly
# - https://www.googleapis.com/auth/drive.metadata.readonly
# - https://www.googleapis.com/auth/calendar.readonly
# - https://www.googleapis.com/auth/calendar.events

# ===========================================
# SECURITY NOTES FOR DEVELOPMENT
# ===========================================
# - This file contains localhost URLs only
# - Never commit actual production credentials to version control
# - Use different JWT_SECRET and ENCRYPTION_KEY for production
# - Production URLs must be set in Elestio environment variables
# - The environment detection system will automatically use these localhost defaults
