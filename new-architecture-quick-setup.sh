#!/bin/bash

# Uru Workspace Platform - New Architecture Quick Setup
# This script helps prepare the new architecture deployment

echo "🚀 Uru Workspace Platform - New Architecture Setup"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: Please run this script from the uru-workspace-platform root directory"
    exit 1
fi

echo "📋 Setting up new architecture configuration..."

# Create backup of current docker-compose.yml
if [ -f "docker-compose.yml" ]; then
    cp docker-compose.yml docker-compose.old.yml.backup
    echo "✅ Backed up current docker-compose.yml to docker-compose.old.yml.backup"
fi

# Use the new architecture docker-compose file
if [ -f "docker-compose.new.yml" ]; then
    cp docker-compose.new.yml docker-compose.yml
    echo "✅ Updated docker-compose.yml for new architecture"
else
    echo "❌ Error: docker-compose.new.yml not found"
    exit 1
fi

# Create environment file for local testing
echo "📝 Creating local environment file for new architecture..."

cat > .env.new-architecture << 'EOF'
# New Architecture Local Development Environment
USE_NEW_ARCHITECTURE=true
NODE_ENV=development
ENVIRONMENT=development
DEBUG=true

# Service URLs - Local Development
FRONTEND_URL=http://localhost:3000
AUTH_SERVICE_URL=http://localhost:8003
INTEGRATIONS_SERVICE_URL=http://localhost:8002
MCP_PROXY_URL=http://localhost:3001
COMPOSIO_SERVICE_URL=http://localhost:8001

# Frontend URLs
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_AUTH_URL=http://localhost:8003
NEXT_PUBLIC_INTEGRATIONS_URL=http://localhost:8002
NEXT_PUBLIC_MCP_URL=http://localhost:3001
NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8001,http://localhost:8002,http://localhost:8003

# Google OAuth - Local Development
GOOGLE_REDIRECT_URI_AUTH=http://localhost:8003/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=http://localhost:8002/oauth/google/callback

# Add your actual credentials here
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here
DATABASE_URL=your_database_url_here
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here
URU_COMPOSIO_API_KEY=your_composio_api_key_here
N8N_SSE_URL=your_n8n_sse_url_here
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
APP_HOST=0.0.0.0
PORT=3000
HOSTNAME=0.0.0.0
EOF

echo "✅ Created .env.new-architecture for local testing"

# Create production environment template
echo "📝 Creating production environment template..."

cat > elestio-production-env.txt << 'EOF'
# Copy these variables to Elestio Environment Variables section

USE_NEW_ARCHITECTURE=true
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false
ELESTIO_DEPLOYMENT=true

# Production Service URLs
FRONTEND_URL=https://app.uruenterprises.com
AUTH_SERVICE_URL=https://auth.uruenterprises.com
INTEGRATIONS_SERVICE_URL=https://integrations.uruenterprises.com
MCP_PROXY_URL=https://mcp.uruenterprises.com
COMPOSIO_SERVICE_URL=http://composio-service:8001

# Frontend Environment Variables
NEXT_PUBLIC_API_URL=https://app.uruenterprises.com
NEXT_PUBLIC_AUTH_URL=https://auth.uruenterprises.com
NEXT_PUBLIC_INTEGRATIONS_URL=https://integrations.uruenterprises.com
NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com
NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true

# CORS Configuration
CORS_ORIGINS=https://app.uruenterprises.com,https://auth.uruenterprises.com,https://integrations.uruenterprises.com,https://mcp.uruenterprises.com

# OAuth Redirect URIs
GOOGLE_REDIRECT_URI_AUTH=https://auth.uruenterprises.com/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=https://integrations.uruenterprises.com/oauth/google/callback

# Keep your existing database and OAuth credentials
# SUPABASE_URL=...
# SUPABASE_KEY=...
# DATABASE_URL=...
# GOOGLE_CLIENT_ID=...
# GOOGLE_CLIENT_SECRET=...
# JWT_SECRET=...
# ENCRYPTION_KEY=...
# URU_COMPOSIO_API_KEY=...
# N8N_SSE_URL=...
# OPENAI_API_KEY=...

APP_HOST=0.0.0.0
PORT=3000
HOSTNAME=0.0.0.0
EOF

echo "✅ Created elestio-production-env.txt"

# Create domain configuration guide
echo "📝 Creating domain configuration guide..."

cat > elestio-domain-setup.md << 'EOF'
# Elestio Domain Configuration for New Architecture

## Required Domain Mappings

Configure these domains in your Elestio dashboard:

1. **app.uruenterprises.com**
   - Port: 3000
   - Path: /
   - Service: Frontend

2. **auth.uruenterprises.com**
   - Port: 8003
   - Path: /
   - Service: Auth Service

3. **integrations.uruenterprises.com**
   - Port: 8002
   - Path: /
   - Service: Integration Service

4. **mcp.uruenterprises.com**
   - Port: 3001
   - Path: /
   - Service: MCP Proxy

## Google OAuth Setup

Add these redirect URIs to your Google Cloud Console OAuth configuration:

- https://auth.uruenterprises.com/oauth/google/callback
- https://integrations.uruenterprises.com/oauth/google/callback

## Health Check URLs

After deployment, verify these endpoints:

- https://app.uruenterprises.com/api/health
- https://auth.uruenterprises.com/health
- https://integrations.uruenterprises.com/health
- https://mcp.uruenterprises.com/health
EOF

echo "✅ Created elestio-domain-setup.md"

echo ""
echo "🎉 New Architecture Setup Complete!"
echo ""
echo "Next Steps:"
echo "1. Review and update credentials in .env.new-architecture"
echo "2. Test locally: docker-compose up --build"
echo "3. Copy elestio-production-env.txt variables to Elestio dashboard"
echo "4. Configure domains as described in elestio-domain-setup.md"
echo "5. Update Google OAuth redirect URIs"
echo "6. Deploy to production"
echo ""
echo "Files created:"
echo "- docker-compose.yml (updated for new architecture)"
echo "- docker-compose.old.yml.backup (backup of old configuration)"
echo "- .env.new-architecture (local development environment)"
echo "- elestio-production-env.txt (production environment variables)"
echo "- elestio-domain-setup.md (domain configuration guide)"
echo ""
echo "For detailed instructions, see: elestio-new-architecture-setup.md"
echo ""
echo "🔧 Would you like to build and test the containers now? (y/n)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "🏗️  Building all containers for development testing..."
    echo "=================================================="

    # Copy environment file for testing
    if [ -f ".env.new-architecture" ]; then
        cp .env.new-architecture .env
        echo "✅ Using new architecture environment for testing"
    fi

    # Build all containers
    echo "🔨 Building containers..."
    docker-compose build --no-cache

    if [ $? -eq 0 ]; then
        echo "✅ All containers built successfully!"
        echo ""
        echo "🚀 Starting services..."
        docker-compose up -d

        echo ""
        echo "⏳ Waiting for services to start..."
        sleep 10

        echo ""
        echo "🔍 Checking service health..."
        echo "Frontend (port 3000): http://localhost:3000"
        echo "Auth Service (port 8003): http://localhost:8003/health"
        echo "Integration Service (port 8002): http://localhost:8002/health"
        echo "MCP Proxy (port 3001): http://localhost:3001/health"
        echo "Composio Service (port 8001): http://localhost:8001/health"

        echo ""
        echo "📋 To view logs: docker-compose logs -f"
        echo "📋 To stop services: docker-compose down"
        echo "📋 To rebuild: docker-compose up --build"
    else
        echo "❌ Container build failed. Check the output above for errors."
        exit 1
    fi
fi
