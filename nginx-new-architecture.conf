# Nginx Configuration for Uru Workspace Platform - New Architecture
# This configuration handles reverse proxy for all services in the new architecture
# For use with Elestio deployment

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=10r/m;
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=30r/m;
limit_req_zone $binary_remote_addr zone=oauth_limit:10m rate=5r/m;

# Upstream definitions for load balancing and health checks
upstream frontend_backend {
    server 172.17.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream auth_backend {
    server 172.17.0.1:8003 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream integrations_backend {
    server 172.17.0.1:8002 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream mcp_backend {
    server 172.17.0.1:3001 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# ===========================================
# FRONTEND SERVICE - app.uruenterprises.com
# ===========================================
server {
    listen 443 ssl http2;
    server_name app.uruenterprises.com;

    # SSL Configuration (managed by Elestio)
    ssl_certificate /etc/letsencrypt/live/app.uruenterprises.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/app.uruenterprises.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://auth.uruenterprises.com https://integrations.uruenterprises.com https://mcp.uruenterprises.com;" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    location / {
        proxy_pass http://frontend_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # Rate limiting
        limit_req zone=api_limit burst=50 nodelay;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://frontend_backend/health;
        access_log off;
    }
}

# ===========================================
# AUTH SERVICE - auth.uruenterprises.com
# ===========================================
server {
    listen 443 ssl http2;
    server_name auth.uruenterprises.com;

    # SSL Configuration (managed by Elestio)
    ssl_certificate /etc/letsencrypt/live/auth.uruenterprises.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/auth.uruenterprises.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Client settings
    client_max_body_size 5M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    location / {
        proxy_pass http://auth_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # Rate limiting for auth endpoints
        limit_req zone=auth_limit burst=20 nodelay;
    }

    # Special handling for OAuth callbacks
    location /oauth/ {
        proxy_pass http://auth_backend/oauth/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # Stricter rate limiting for OAuth
        limit_req zone=oauth_limit burst=10 nodelay;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://auth_backend/health;
        access_log off;
    }
}

# ===========================================
# INTEGRATION SERVICE - integrations.uruenterprises.com
# ===========================================
server {
    listen 443 ssl http2;
    server_name integrations.uruenterprises.com;

    # SSL Configuration (managed by Elestio)
    ssl_certificate /etc/letsencrypt/live/integrations.uruenterprises.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/integrations.uruenterprises.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Client settings for file uploads
    client_max_body_size 50M;
    client_body_timeout 120s;
    client_header_timeout 60s;

    location / {
        proxy_pass http://integrations_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # Rate limiting
        limit_req zone=api_limit burst=30 nodelay;
    }

    # OAuth callback handling
    location /oauth/ {
        proxy_pass http://integrations_backend/oauth/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # OAuth rate limiting
        limit_req zone=oauth_limit burst=10 nodelay;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://integrations_backend/health;
        access_log off;
    }
}

# ===========================================
# MCP PROXY SERVICE - mcp.uruenterprises.com
# ===========================================
server {
    listen 443 ssl http2;
    server_name mcp.uruenterprises.com;

    # SSL Configuration (managed by Elestio)
    ssl_certificate /etc/letsencrypt/live/mcp.uruenterprises.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/mcp.uruenterprises.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    location / {
        proxy_pass http://mcp_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Enhanced timeouts for SSE connections
        proxy_read_timeout 3600s;  # 1 hour for persistent SSE
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;

        # SSE-specific optimizations
        proxy_buffering off;  # Disable buffering for real-time streaming
        proxy_cache off;      # Disable caching for SSE

        # Rate limiting
        limit_req zone=api_limit burst=50 nodelay;
    }

    # Specific location for SSE diagnostic endpoints
    location /api/sse/ {
        proxy_pass http://mcp_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Optimized for diagnostic endpoints
        proxy_read_timeout 30s;
        proxy_connect_timeout 10s;
        proxy_buffering off;

        # Allow more frequent access to diagnostics
        limit_req zone=api_limit burst=20 nodelay;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://mcp_backend/health;
        access_log off;
    }
}

# ===========================================
# HTTP to HTTPS Redirects
# ===========================================
server {
    listen 80;
    server_name app.uruenterprises.com auth.uruenterprises.com integrations.uruenterprises.com mcp.uruenterprises.com;
    return 301 https://$server_name$request_uri;
}
