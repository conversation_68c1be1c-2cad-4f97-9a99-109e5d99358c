# Environment variables (CRITICAL - NEVER COMMIT THESE)
.env
.env.*
!.env.development
oauth-service/.env
oauth-service/.env.*
mcp-proxy/.env
mcp-proxy/.env.*
frontend/.env
frontend/.env.*
!frontend/.env.development

# Dependencies
node_modules/
*/node_modules/
__pycache__/
*/__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
oauth-service/venv/
oauth-service/env/

# Production builds
.next/
out/
build/
dist/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Database
*.db
*.sqlite
*.sqlite3

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp
temp/
tmp/

# SSL certificates
*.pem
*.key
*.crt

# Secrets and credentials (CRITICAL - NEVER COMMIT THESE)
secrets/
credentials/
config/mcp-gdrive/
*.json
!package*.json
!tsconfig.json
!next.config.js

# API Keys and tokens (be more specific to avoid excluding legitimate directories)
*api-key*
*secret-key*
*access-token*
*refresh-token*
*credential-file*
# Allow auth directories and services
!oauth-service/
!**/auth/
!**/auth.py
!**/auth.js
!**/auth.ts
!**/auth.tsx

# OAuth and authentication files
oauth-config.json
google-credentials.json
service-account*.json
client-secret*.json

# Database dumps and backups
*.sql.gz
*.dump
*.backup
database-backup*
db-dump*

# Configuration files with sensitive data
config.json
settings.json
local-config*
production-config*

# Testing
coverage/
.nyc_output
test-*.js
test_*.py
*test*.html
debug_*.py
diagnose-*.js
oauth_*_analyzer.py
oauth_callback_failure_analysis.py
simple-*.js
quick-*.js
quick-*.ps1
troubleshoot-*.js
validate-*.js
verify_*.py
fix-*.ps1
fix-*.sh
fix-*.py
fix-*.js

# Terraform (if you use it later)
*.tfstate
*.tfstate.*
.terraform/

# Backup files
*.bak
*.backup

# Local development
.env.local
local.env

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries/
.idea/shelf/
*.sublime-project
*.sublime-workspace

# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Docker development
docker-compose.override.yml
.dockerignore

# Elestio deployment files
elestio-*.env
elestio-config.json
deployment-*.log

# Monitoring and analytics
*.log
logs/
monitoring/
analytics/

# Performance and profiling
*.prof
*.trace
*.heap
performance-*.json