#!/bin/bash

# Uru Workspace Platform - Development Container Builder
# Builds all containers for the new architecture and runs health checks

set -e  # Exit on any error

echo "🏗️  Uru Workspace Platform - Development Container Builder"
echo "========================================================"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: Please run this script from the uru-workspace-platform root directory"
    exit 1
fi

# Function to check if a service is healthy
check_service_health() {
    local service_name=$1
    local health_url=$2
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Checking $service_name health..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            echo "✅ $service_name is healthy"
            return 0
        fi
        
        echo "⏳ Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to become healthy after $max_attempts attempts"
    return 1
}

# Function to show service logs
show_service_logs() {
    echo ""
    echo "📋 Recent logs from all services:"
    echo "================================="
    docker-compose logs --tail=10
}

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down --remove-orphans 2>/dev/null || true

# Remove old images to ensure fresh build
echo "🗑️  Removing old images for fresh build..."
docker-compose down --rmi all 2>/dev/null || true

# Create development environment if it doesn't exist
if [ ! -f ".env" ]; then
    if [ -f ".env.new-architecture" ]; then
        cp .env.new-architecture .env
        echo "✅ Created .env from .env.new-architecture template"
        echo "⚠️  Please update the credentials in .env file before proceeding"
        echo ""
        echo "Required credentials to update:"
        echo "- SUPABASE_URL"
        echo "- SUPABASE_KEY" 
        echo "- DATABASE_URL"
        echo "- GOOGLE_CLIENT_ID"
        echo "- GOOGLE_CLIENT_SECRET"
        echo "- JWT_SECRET"
        echo "- ENCRYPTION_KEY"
        echo ""
        read -p "Press Enter after updating .env file to continue..."
    else
        echo "❌ No environment file found. Please create .env file first."
        exit 1
    fi
fi

echo ""
echo "🔨 Building all containers (this may take a few minutes)..."
echo "========================================================="

# Build each service individually to show progress
services=("auth-service" "integration-service" "composio-service" "mcp-proxy" "frontend")

for service in "${services[@]}"; do
    echo ""
    echo "🔨 Building $service..."
    if docker-compose build --no-cache "$service"; then
        echo "✅ $service built successfully"
    else
        echo "❌ Failed to build $service"
        exit 1
    fi
done

echo ""
echo "🚀 Starting all services..."
echo "==========================="

# Start services in dependency order
docker-compose up -d

echo ""
echo "⏳ Waiting for services to initialize..."
sleep 15

echo ""
echo "🔍 Performing health checks..."
echo "=============================="

# Health check URLs
declare -A health_checks=(
    ["Auth Service"]="http://localhost:8003/health"
    ["Integration Service"]="http://localhost:8002/health"
    ["Composio Service"]="http://localhost:8001/health"
    ["MCP Proxy"]="http://localhost:3001/health"
    ["Frontend"]="http://localhost:3000"
)

all_healthy=true

for service in "${!health_checks[@]}"; do
    if ! check_service_health "$service" "${health_checks[$service]}"; then
        all_healthy=false
    fi
done

echo ""
echo "📊 Service Status Summary:"
echo "========================="
docker-compose ps

if [ "$all_healthy" = true ]; then
    echo ""
    echo "🎉 All services are running and healthy!"
    echo ""
    echo "🌐 Service URLs:"
    echo "- Frontend: http://localhost:3000"
    echo "- Auth Service: http://localhost:8003"
    echo "- Integration Service: http://localhost:8002"
    echo "- MCP Proxy: http://localhost:3001"
    echo "- Composio Service: http://localhost:8001"
    echo ""
    echo "🔍 Health Check URLs:"
    echo "- Auth: http://localhost:8003/health"
    echo "- Integration: http://localhost:8002/health"
    echo "- MCP: http://localhost:3001/health"
    echo "- Composio: http://localhost:8001/health"
    echo ""
    echo "📋 Useful Commands:"
    echo "- View logs: docker-compose logs -f"
    echo "- View specific service logs: docker-compose logs -f [service-name]"
    echo "- Stop services: docker-compose down"
    echo "- Restart services: docker-compose restart"
    echo "- Rebuild and restart: docker-compose up --build"
    echo ""
    echo "🧪 Test the new architecture:"
    echo "1. Open http://localhost:3000 in your browser"
    echo "2. Try authentication flow"
    echo "3. Test OAuth integrations"
    echo "4. Verify MCP proxy functionality"
    
else
    echo ""
    echo "⚠️  Some services are not healthy. Showing recent logs..."
    show_service_logs
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Check service logs: docker-compose logs -f [service-name]"
    echo "2. Verify environment variables in .env file"
    echo "3. Ensure all required credentials are set"
    echo "4. Check if ports are already in use"
    echo ""
    echo "Common issues:"
    echo "- Missing or invalid database credentials"
    echo "- Missing Google OAuth credentials"
    echo "- Port conflicts (check if services are already running)"
    echo "- Network connectivity issues"
fi

echo ""
echo "🏁 Development container build complete!"
