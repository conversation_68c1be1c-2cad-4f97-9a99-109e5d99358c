# Copy these variables to Elestio Environment Variables section

USE_NEW_ARCHITECTURE=true
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false
ELESTIO_DEPLOYMENT=true

# Production Service URLs
FRONTEND_URL=https://app.uruenterprises.com
AUTH_SERVICE_URL=https://auth.uruenterprises.com
INTEGRATIONS_SERVICE_URL=https://integrations.uruenterprises.com
MCP_PROXY_URL=https://mcp.uruenterprises.com
COMPOSIO_SERVICE_URL=http://composio-service:8001

# Frontend Environment Variables
NEXT_PUBLIC_API_URL=https://app.uruenterprises.com
NEXT_PUBLIC_AUTH_URL=https://auth.uruenterprises.com
NEXT_PUBLIC_INTEGRATIONS_URL=https://integrations.uruenterprises.com
NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com
NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true

# CORS Configuration
CORS_ORIGINS=https://app.uruenterprises.com,https://auth.uruenterprises.com,https://integrations.uruenterprises.com,https://mcp.uruenterprises.com

# OAuth Redirect URIs
GOOGLE_REDIRECT_URI_AUTH=https://auth.uruenterprises.com/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=https://integrations.uruenterprises.com/oauth/google/callback

# Keep your existing database and OAuth credentials
# SUPABASE_URL=...
# SUPABASE_KEY=...
# DATABASE_URL=...
# GOOGLE_CLIENT_ID=...
# GOOGLE_CLIENT_SECRET=...
# JWT_SECRET=...
# ENCRYPTION_KEY=...
# URU_COMPOSIO_API_KEY=...
# N8N_SSE_URL=...
# OPENAI_API_KEY=...

APP_HOST=0.0.0.0
PORT=3000
HOSTNAME=0.0.0.0
