# Elestio New Architecture Setup Guide

## Overview
This guide helps you migrate from the old architecture (oauth-service) to the new architecture (auth-service + integration-service) on Elestio.

## New Architecture Services

### Service Mapping
| Service | Port | Production URL | Purpose |
|---------|------|----------------|---------|
| Frontend | 3000 | https://app.uruenterprises.com | Next.js frontend |
| Auth Service | 8003 | https://auth.uruenterprises.com | User authentication |
| Integration Service | 8002 | https://integrations.uruenterprises.com | OAuth integrations |
| MCP Proxy | 3001 | https://mcp.uruenterprises.com | MCP protocol proxy |
| Composio Service | 8001 | (internal only) | Composio integration |

## Step 1: Update Environment Variables in Elestio

Copy these environment variables to your Elestio environment variables section:

```env
# Architecture Configuration
USE_NEW_ARCHITECTURE=true
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false
ELESTIO_DEPLOYMENT=true

# Database (keep existing)
SUPABASE_URL=https://sipvdxjupgnymlshsoro.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNpcHZkeGp1cGdueW1sc2hzb3JvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0ODQzODIsImV4cCI6MjA2NjA2MDM4Mn0.iY-BFFxDXkbI1qiPGqK9u9Y-F5MWuYptTsmSlLuehIs
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Security (keep existing)
JWT_SECRET=uru-workspace-platform-super-secret-jwt-key-2025
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
ENCRYPTION_KEY=gAAAAABnZGVmYXVsdC1lbmNyeXB0aW9uLWtleS1mb3ItdXJ1LXBsYXRmb3Jt

# Google OAuth (keep existing client credentials)
GOOGLE_CLIENT_ID=949099265240-g5p5m29mdpr2jisrbmbkksm84f9q89bv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# NEW: Separate OAuth redirect URIs for each service
GOOGLE_REDIRECT_URI_AUTH=https://auth.uruenterprises.com/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=https://integrations.uruenterprises.com/oauth/google/callback

# Service URLs - NEW ARCHITECTURE
FRONTEND_URL=https://app.uruenterprises.com
AUTH_SERVICE_URL=https://auth.uruenterprises.com
INTEGRATIONS_SERVICE_URL=https://integrations.uruenterprises.com
MCP_PROXY_URL=https://mcp.uruenterprises.com
COMPOSIO_SERVICE_URL=http://composio-service:8001

# Frontend Environment Variables
NEXT_PUBLIC_API_URL=https://app.uruenterprises.com
NEXT_PUBLIC_AUTH_URL=https://auth.uruenterprises.com
NEXT_PUBLIC_INTEGRATIONS_URL=https://integrations.uruenterprises.com
NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com
NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true

# CORS Configuration
CORS_ORIGINS=https://app.uruenterprises.com,https://auth.uruenterprises.com,https://integrations.uruenterprises.com,https://mcp.uruenterprises.com

# External Services (keep existing)
N8N_SSE_URL=https://n8n-uru-u46170.vm.elestio.app/mcp/uru-workspace-platform/sse
URU_COMPOSIO_API_KEY=your_composio_api_key_here
URU_COMPOSIO_BASE_URL=https://backend.composio.dev/api
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
APP_HOST=0.0.0.0
PORT=3000
HOSTNAME=0.0.0.0
```

## Step 2: Update Google OAuth Configuration

### Add New Redirect URIs in Google Cloud Console

1. Go to Google Cloud Console → APIs & Services → Credentials
2. Edit your OAuth 2.0 Client ID
3. Add these new Authorized redirect URIs:
   - `https://auth.uruenterprises.com/oauth/google/callback`
   - `https://integrations.uruenterprises.com/oauth/google/callback`
4. Keep the existing redirect URI for backward compatibility during migration

## Step 3: Elestio Reverse Proxy Configuration

### Domain Setup
You'll need to configure these domains in Elestio:

1. **app.uruenterprises.com** → Port 3000 (Frontend)
2. **auth.uruenterprises.com** → Port 8003 (Auth Service)
3. **integrations.uruenterprises.com** → Port 8002 (Integration Service)
4. **mcp.uruenterprises.com** → Port 3001 (MCP Proxy)

### Elestio Configuration Steps

1. In Elestio dashboard, go to your service
2. Navigate to "Domains" section
3. Add each domain with corresponding port mapping:
   - Domain: `app.uruenterprises.com`, Port: `3000`, Path: `/`
   - Domain: `auth.uruenterprises.com`, Port: `8003`, Path: `/`
   - Domain: `integrations.uruenterprises.com`, Port: `8002`, Path: `/`
   - Domain: `mcp.uruenterprises.com`, Port: `3001`, Path: `/`

## Step 4: Update Docker Compose

Replace your current `docker-compose.yml` with `docker-compose.new.yml` (already created) or update the existing one to use the new architecture services.

## Step 5: Deploy and Test

1. Push your code changes to trigger Elestio rebuild
2. Monitor logs for each service startup
3. Test each endpoint:
   - https://app.uruenterprises.com (Frontend)
   - https://auth.uruenterprises.com/health (Auth Service)
   - https://integrations.uruenterprises.com/health (Integration Service)
   - https://mcp.uruenterprises.com/health (MCP Proxy)

## Migration Strategy

### Option A: Blue-Green Deployment
1. Deploy new architecture alongside old
2. Test thoroughly
3. Switch DNS/routing when ready
4. Decommission old services

### Option B: In-Place Migration
1. Update environment variables
2. Replace docker-compose.yml
3. Deploy new architecture
4. Update DNS records

## Rollback Plan

If issues occur:
1. Set `USE_NEW_ARCHITECTURE=false`
2. Revert to old docker-compose.yml
3. Update DNS back to old configuration
4. Investigate and fix issues before retry

## Health Check URLs

- Frontend: https://app.uruenterprises.com/api/health
- Auth: https://auth.uruenterprises.com/health
- Integrations: https://integrations.uruenterprises.com/health
- MCP Proxy: https://mcp.uruenterprises.com/health
