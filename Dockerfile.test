FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \
    requests \
    python-dotenv

# Copy test scripts
COPY scripts/test-integration-system.py /app/test-integration-system.py
COPY scripts/setup-integration-system.py /app/setup-integration-system.py

# Make scripts executable
RUN chmod +x /app/test-integration-system.py
RUN chmod +x /app/setup-integration-system.py

# Default command
CMD ["python", "/app/test-integration-system.py"]
