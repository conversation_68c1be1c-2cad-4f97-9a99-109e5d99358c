"""
Shared Environment Detection and Configuration Utilities
Provides consistent environment detection across all services
"""

import os
import socket
from typing import Dict, List, Optional
from enum import Enum

class Environment(Enum):
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"

class EnvironmentConfig:
    """Centralized environment configuration for all services"""
    
    def __init__(self):
        self.environment = self._detect_environment()
        self.is_development = self.environment == Environment.DEVELOPMENT
        self.is_production = self.environment == Environment.PRODUCTION
        self.is_testing = self.environment == Environment.TESTING
        
    def _detect_environment(self) -> Environment:
        """Detect the current environment based on various indicators"""
        
        # Check explicit environment variable first
        env_var = os.getenv("ENVIRONMENT", "").lower()
        if env_var in ["production", "prod"]:
            return Environment.PRODUCTION
        elif env_var in ["development", "dev", "local"]:
            return Environment.DEVELOPMENT
        elif env_var in ["testing", "test"]:
            return Environment.TESTING
        
        # Check NODE_ENV for consistency with frontend
        node_env = os.getenv("NODE_ENV", "").lower()
        if node_env == "production":
            return Environment.PRODUCTION
        elif node_env in ["development", "dev"]:
            return Environment.DEVELOPMENT
        
        # Check for production indicators
        production_indicators = [
            os.getenv("ELESTIO_DEPLOYMENT"),  # Elestio-specific
            os.getenv("RAILWAY_DEPLOYMENT"),  # Railway-specific
            os.getenv("VERCEL_DEPLOYMENT"),   # Vercel-specific
            "uruenterprises.com" in os.getenv("OAUTH_SERVICE_URL", ""),
            "uruenterprises.com" in os.getenv("FRONTEND_URL", ""),
        ]
        
        if any(production_indicators):
            return Environment.PRODUCTION
        
        # Check for localhost/development indicators
        development_indicators = [
            "localhost" in os.getenv("OAUTH_SERVICE_URL", ""),
            "127.0.0.1" in os.getenv("OAUTH_SERVICE_URL", ""),
            os.getenv("DEBUG", "").lower() == "true",
            self._is_running_locally(),
        ]
        
        if any(development_indicators):
            return Environment.DEVELOPMENT
        
        # Default to development for safety
        return Environment.DEVELOPMENT
    
    def _is_running_locally(self) -> bool:
        """Check if we're running on a local development machine"""
        try:
            # Check if we can resolve localhost
            socket.gethostbyname("localhost")
            
            # Check for common development environment indicators
            local_indicators = [
                os.path.exists("/.dockerenv") and os.getenv("HOSTNAME", "").startswith("localhost"),
                os.getenv("USER") in ["developer", "dev", "admin"],
                os.path.exists("./docker-compose.yml"),
                os.path.exists("./.env"),
                os.path.exists("./package.json"),
            ]
            
            return any(local_indicators)
        except:
            return False
    
    def get_auth_service_url(self) -> str:
        """Get the appropriate Auth service URL for the current environment"""
        # Always check environment variable first
        override = os.getenv("AUTH_SERVICE_URL")
        if override:
            return override

        # Only provide localhost default for development
        if self.is_development:
            return "http://localhost:8003"
        else:
            # Production must set AUTH_SERVICE_URL environment variable
            raise ValueError("AUTH_SERVICE_URL environment variable must be set for production deployment")

    def get_integrations_service_url(self) -> str:
        """Get the appropriate Integrations service URL for the current environment"""
        # Always check environment variable first
        override = os.getenv("INTEGRATIONS_SERVICE_URL")
        if override:
            return override

        # Only provide localhost default for development
        if self.is_development:
            return "http://localhost:8002"
        else:
            # Production must set INTEGRATIONS_SERVICE_URL environment variable
            raise ValueError("INTEGRATIONS_SERVICE_URL environment variable must be set for production deployment")

    def get_composio_service_url(self) -> str:
        """Get the appropriate Composio service URL for the current environment"""
        # Always check environment variable first
        override = os.getenv("COMPOSIO_SERVICE_URL")
        if override:
            return override

        # Only provide localhost default for development
        if self.is_development:
            return "http://localhost:8001"
        else:
            # Production must set COMPOSIO_SERVICE_URL environment variable
            raise ValueError("COMPOSIO_SERVICE_URL environment variable must be set for production deployment")
    
    def get_mcp_proxy_url(self) -> str:
        """Get the appropriate MCP proxy URL for the current environment"""
        # Always check environment variable first
        override = os.getenv("MCP_PROXY_URL")
        if override:
            return override

        # Only provide localhost default for development
        if self.is_development:
            return "http://localhost:3001"
        else:
            # Production must set MCP_PROXY_URL environment variable
            raise ValueError("MCP_PROXY_URL environment variable must be set for production deployment")
    
    def get_frontend_url(self) -> str:
        """Get the appropriate frontend URL for the current environment"""
        # Always check environment variable first
        override = os.getenv("FRONTEND_URL")
        if override:
            return override

        # Only provide localhost default for development
        if self.is_development:
            return "http://localhost:3000"
        else:
            # Production must set FRONTEND_URL environment variable
            raise ValueError("FRONTEND_URL environment variable must be set for production deployment")
    
    def get_google_redirect_uri(self) -> str:
        """Get the appropriate Google OAuth redirect URI for the current environment"""
        # Always check environment variable first
        override = os.getenv("GOOGLE_REDIRECT_URI")
        if override:
            return override

        # Build from OAuth service URL (which handles environment detection properly)
        try:
            oauth_url = self.get_oauth_service_url()
            return f"{oauth_url}/oauth/google/callback"
        except ValueError:
            # If OAuth service URL is not set, we can't build redirect URI
            raise ValueError("GOOGLE_REDIRECT_URI or OAUTH_SERVICE_URL environment variable must be set for production deployment")
    
    def get_cors_origins(self) -> List[str]:
        """Get the appropriate CORS origins for the current environment"""
        # Always check environment variable first
        override = os.getenv("CORS_ORIGINS")
        if override:
            try:
                # Try JSON parsing first
                import json
                return json.loads(override)
            except:
                try:
                    # Fall back to comma-separated parsing
                    return [origin.strip() for origin in override.split(",") if origin.strip()]
                except:
                    pass

        # Only provide localhost defaults for development
        if self.is_development:
            return [
                "http://localhost:3000",
                "http://localhost:3001",
                "http://localhost:8001",
                "http://localhost:8002",
                "http://localhost:8003",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:3001",
                "http://127.0.0.1:8001",
                "http://127.0.0.1:8002",
                "http://127.0.0.1:8003"
            ]
        else:
            # Production must set CORS_ORIGINS environment variable
            raise ValueError("CORS_ORIGINS environment variable must be set for production deployment")
    
    def get_all_cors_origins(self) -> List[str]:
        """Get CORS origins for both environments (useful for development)"""
        development_origins = [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://localhost:8001",
            "http://localhost:8002",
            "http://localhost:8003",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://127.0.0.1:8001",
            "http://127.0.0.1:8002",
            "http://127.0.0.1:8003"
        ]

        # In development, allow localhost origins plus any production origins from env vars
        if self.is_development:
            # Start with development origins
            all_origins = development_origins.copy()

            # Add production origins from environment variables if available
            override = os.getenv("CORS_ORIGINS")
            if override:
                try:
                    # Try JSON parsing first
                    import json
                    prod_origins = json.loads(override)
                    all_origins.extend(prod_origins)
                except:
                    try:
                        # Fall back to comma-separated parsing
                        prod_origins = [origin.strip() for origin in override.split(",") if origin.strip()]
                        all_origins.extend(prod_origins)
                    except:
                        pass

            return all_origins
        else:
            # Production uses only environment variable
            return self.get_cors_origins()
    
    def get_database_config(self) -> Dict[str, str]:
        """Get database configuration for the current environment"""
        return {
            "url": os.getenv("SUPABASE_URL", ""),
            "key": os.getenv("SUPABASE_KEY", ""),
            "database_url": os.getenv("DATABASE_URL", "")
        }
    
    def get_google_oauth_config(self) -> Dict[str, str]:
        """Get Google OAuth configuration for the current environment"""
        return {
            "client_id": os.getenv("GOOGLE_CLIENT_ID", ""),
            "client_secret": os.getenv("GOOGLE_CLIENT_SECRET", ""),
            "redirect_uri": self.get_google_redirect_uri()
        }
    
    def get_security_config(self) -> Dict[str, str]:
        """Get security configuration for the current environment"""
        return {
            "jwt_secret": os.getenv("JWT_SECRET", "dev-secret-key" if self.is_development else ""),
            "encryption_key": os.getenv("ENCRYPTION_KEY", ""),
            "debug": str(self.is_development).lower()
        }
    
    def print_config_summary(self):
        """Print a summary of the current configuration"""
        print(f" Environment: {self.environment.value}")
        print(f" OAuth Service: {self.get_oauth_service_url()}")
        print(f" MCP Proxy: {self.get_mcp_proxy_url()}")
        print(f" Frontend: {self.get_frontend_url()}")
        print(f" Redirect URI: {self.get_google_redirect_uri()}")
        print(f" CORS Origins: {len(self.get_cors_origins())} configured")
        print(f"🐛 Debug Mode: {self.is_development}")

# Global instance for easy importing
env_config = EnvironmentConfig()

# Convenience functions for backward compatibility
def get_environment() -> Environment:
    return env_config.environment

def is_development() -> bool:
    return env_config.is_development

def is_production() -> bool:
    return env_config.is_production

def get_oauth_service_url() -> str:
    return env_config.get_oauth_service_url()

def get_mcp_proxy_url() -> str:
    return env_config.get_mcp_proxy_url()

def get_frontend_url() -> str:
    return env_config.get_frontend_url()

def get_cors_origins() -> List[str]:
    return env_config.get_all_cors_origins()

if __name__ == "__main__":
    # Print configuration when run directly
    env_config.print_config_summary()
