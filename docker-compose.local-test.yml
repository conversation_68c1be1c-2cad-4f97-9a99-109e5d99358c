version: '3.8'

services:
  # Auth Service
  auth-service:
    build: ./auth-service
    ports:
      - "8000:8000"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - FRONTEND_URL=http://localhost:3000
    networks:
      - uru-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Integration Service
  integration-service:
    build: ./integration-service
    ports:
      - "8002:8002"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - AUTH_SERVICE_URL=http://auth-service:8000
      - FRONTEND_URL=http://localhost:3000
    networks:
      - uru-network
    depends_on:
      - auth-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Composio Service
  composio-service:
    build: ./composio-service
    ports:
      - "8001:8001"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - AUTH_SERVICE_URL=http://auth-service:8000
      - FRONTEND_URL=http://localhost:3000
      # Google OAuth
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      # Slack OAuth
      - SLACK_CLIENT_ID=${SLACK_CLIENT_ID}
      - SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET}
      # Microsoft OAuth
      - MICROSOFT_CLIENT_ID=${MICROSOFT_CLIENT_ID}
      - MICROSOFT_CLIENT_SECRET=${MICROSOFT_CLIENT_SECRET}
      # Notion OAuth
      - NOTION_CLIENT_ID=${NOTION_CLIENT_ID}
      - NOTION_CLIENT_SECRET=${NOTION_CLIENT_SECRET}
      # Airtable OAuth
      - AIRTABLE_CLIENT_ID=${AIRTABLE_CLIENT_ID}
      - AIRTABLE_CLIENT_SECRET=${AIRTABLE_CLIENT_SECRET}
      # Asana OAuth
      - ASANA_CLIENT_ID=${ASANA_CLIENT_ID}
      - ASANA_CLIENT_SECRET=${ASANA_CLIENT_SECRET}
      # HubSpot OAuth
      - HUBSPOT_CLIENT_ID=${HUBSPOT_CLIENT_ID}
      - HUBSPOT_CLIENT_SECRET=${HUBSPOT_CLIENT_SECRET}
      # Salesforce OAuth
      - SALESFORCE_CLIENT_ID=${SALESFORCE_CLIENT_ID}
      - SALESFORCE_CLIENT_SECRET=${SALESFORCE_CLIENT_SECRET}
    networks:
      - uru-network
    depends_on:
      - auth-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCP Proxy
  mcp-proxy:
    build: ./mcp-proxy
    ports:
      - "3001:3001"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - AUTH_SERVICE_URL=http://auth-service:8000
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
      - N8N_SSE_URL=${N8N_SSE_URL}
      - N8N_TOKEN=${N8N_TOKEN}
      - USE_NEW_ARCHITECTURE=true
    networks:
      - uru-network
    depends_on:
      - auth-service
      - composio-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_AUTH_URL=http://localhost:8000
      - NEXT_PUBLIC_OAUTH_URL=http://localhost:8000
      - NEXT_PUBLIC_COMPOSIO_URL=http://localhost:8001
      - NEXT_PUBLIC_MCP_URL=http://localhost:3001
      - NEXT_PUBLIC_INTEGRATIONS_URL=http://localhost:8002
    networks:
      - uru-network
    depends_on:
      - auth-service
      - composio-service
      - mcp-proxy

  # Test Runner
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - AUTH_SERVICE_URL=http://auth-service:8000
      - COMPOSIO_SERVICE_URL=http://composio-service:8001
      - MCP_PROXY_URL=http://mcp-proxy:3001
      - FRONTEND_URL=http://frontend:3000
      - TEST_EMAIL=${TEST_EMAIL:-<EMAIL>}
      - TEST_PASSWORD=${TEST_PASSWORD:-testpassword123}
    networks:
      - uru-network
    depends_on:
      - auth-service
      - composio-service
      - mcp-proxy
      - frontend
    profiles:
      - test

networks:
  uru-network:
    driver: bridge

volumes:
  postgres_data:
