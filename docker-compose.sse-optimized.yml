# Docker Compose Override for SSE Optimization
# Use with: docker-compose -f docker-compose.yml -f docker-compose.sse-optimized.yml up
# This file contains SSE-specific optimizations for reliable connections

version: '3.8'

services:
  # ===========================================
  # MCP PROXY - SSE OPTIMIZED
  # ===========================================
  
  mcp-proxy:
    environment:
      # SSE-specific environment variables
      - SSE_CONNECTION_TIMEOUT=30000
      - SSE_RETRY_DELAY=1000
      - SSE_MAX_RETRIES=10
      - SSE_HEARTBEAT_INTERVAL=30000
      - SSE_HEALTH_CHECK_INTERVAL=60000
      
      # Enhanced logging for SSE debugging
      - DEBUG_SSE=true
      - LOG_LEVEL=debug
      
      # Network optimization
      - NODE_OPTIONS=--max-old-space-size=512
      - UV_THREADPOOL_SIZE=4
      
    # Resource limits for stable SSE connections
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Enhanced health check for SSE monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/sse/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy for connection stability
    restart: unless-stopped
    
    # Additional labels for monitoring
    labels:
      - "uru.service=mcp-proxy"
      - "uru.sse.enabled=true"
      - "uru.monitoring.health_endpoint=/api/sse/status"

  # ===========================================
  # FRONTEND - SSE CLIENT OPTIMIZED
  # ===========================================
  
  frontend:
    environment:
      # SSE client configuration
      - NEXT_PUBLIC_SSE_ENABLED=true
      - NEXT_PUBLIC_SSE_RECONNECT_INTERVAL=5000
      - NEXT_PUBLIC_SSE_MAX_RECONNECT_ATTEMPTS=10
      
      # Enhanced error handling
      - NEXT_PUBLIC_DEBUG_SSE=true
    
    # Ensure MCP proxy is healthy before starting frontend
    depends_on:
      mcp-proxy:
        condition: service_healthy

# ===========================================
# NETWORK OPTIMIZATION
# ===========================================

networks:
  uru-network:
    driver: bridge
    driver_opts:
      # Optimize for persistent connections
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: "1500"
    ipam:
      config:
        - subnet: **********/16

# ===========================================
# VOLUMES FOR SSE MONITORING
# ===========================================

volumes:
  sse_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/sse
  
  sse_diagnostics:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./diagnostics/sse
