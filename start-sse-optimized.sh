#!/bin/bash
# SSE-Optimized Startup Script for Local Development

echo "🔧 Starting Uru Workspace Platform with SSE optimization..."
echo "📍 Environment: Local Development"

# Load SSE environment variables
if [ -f ".env.sse.local" ]; then
    echo "📝 Loading SSE configuration..."
    export $(cat .env.sse.local | grep -v '^#' | xargs)
fi

# Start services with appropriate configuration
if [ "local" = "elestio" ]; then
    echo "🌐 Starting for Elestio production..."
    docker-compose -f docker-compose.yml -f docker-compose.elestio.yml up -d
elif [ "local" = "docker" ]; then
    echo "🐳 Starting for Docker development..."
    docker-compose -f docker-compose.yml -f docker-compose.docker.yml up -d
else
    echo "🏠 Starting for local development..."
    docker-compose up -d
fi

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 10

# Check SSE connection
echo "🔍 Checking SSE connection..."
if command -v curl &> /dev/null; then
    SSE_STATUS=$(curl -s http://localhost:3001/api/sse/status 2>/dev/null || echo "failed")
    if [[ "$SSE_STATUS" == *"connected"* ]]; then
        echo "✅ SSE connection established"
    else
        echo "⚠️ SSE connection not ready - check logs"
    fi
else
    echo "ℹ️ curl not available - check SSE status manually"
fi

echo "🎉 Startup completed!"
echo "📊 Monitor SSE health: http://localhost:3001/api/sse/status"
echo "🔧 SSE diagnostics: http://localhost:3001/api/sse/diagnostics"
