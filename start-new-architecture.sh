#!/bin/bash

# Quick start script for new architecture development
echo "🚀 Starting Uru Workspace Platform - New Architecture"
echo "===================================================="

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Creating from example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env from .env.example"
        echo ""
        echo "🔧 Please update the following credentials in .env:"
        echo "- SUPABASE_URL"
        echo "- SUPABASE_KEY"
        echo "- DATABASE_URL"
        echo "- GOOGLE_CLIENT_ID"
        echo "- GOOGLE_CLIENT_SECRET"
        echo "- JWT_SECRET"
        echo "- ENCRYPTION_KEY"
        echo ""
        read -p "Press Enter after updating .env to continue..."
    else
        echo "❌ .env.example not found!"
        exit 1
    fi
fi

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down --remove-orphans 2>/dev/null || true

# Build and start services
echo "🏗️  Building and starting all services..."
docker-compose -f docker-compose.new.yml up --build -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 15

echo ""
echo "🔍 Checking service status..."
docker-compose -f docker-compose.new.yml ps

echo ""
echo "🌐 Service URLs:"
echo "- Frontend: http://localhost:3000"
echo "- Auth Service: http://localhost:8003/health"
echo "- Integration Service: http://localhost:8002/health"
echo "- MCP Proxy: http://localhost:3001/health"
echo "- Composio Service: http://localhost:8001/health"

echo ""
echo "📋 Useful commands:"
echo "- View logs: docker-compose -f docker-compose.new.yml logs -f"
echo "- Stop services: docker-compose -f docker-compose.new.yml down"
echo "- Restart: docker-compose -f docker-compose.new.yml restart"

echo ""
echo "✅ New architecture started! Check the URLs above to verify services are running."
