# Uru Workspace Platform - New Architecture Environment Variables
# For Elestio Production Deployment
# Upload this file to Elestio Environment Variables section

# ===========================================
# ARCHITECTURE CONFIGURATION
# ===========================================
USE_NEW_ARCHITECTURE=true

# ===========================================
# ENVIRONMENT DETECTION
# ===========================================
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false
ELESTIO_DEPLOYMENT=true

# ===========================================
# SUPABASE DATABASE CONFIGURATION
# ===========================================
SUPABASE_URL=https://sipvdxjupgnymlshsoro.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNpcHZkeGp1cGdueW1sc2hzb3JvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0ODQzODIsImV4cCI6MjA2NjA2MDM4Mn0.iY-BFFxDXkbI1qiPGqK9u9Y-F5MWuYptTsmSlLuehIs
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# ===========================================
# AUTHENTICATION & SECURITY
# ===========================================
JWT_SECRET=uru-workspace-platform-super-secret-jwt-key-2025
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
ENCRYPTION_KEY=gAAAAABnZGVmYXVsdC1lbmNyeXB0aW9uLWtleS1mb3ItdXJ1LXBsYXRmb3Jt

# ===========================================
# GOOGLE OAUTH CONFIGURATION
# ===========================================
GOOGLE_CLIENT_ID=949099265240-g5p5m29mdpr2jisrbmbkksm84f9q89bv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# New Architecture OAuth Redirect URIs
GOOGLE_REDIRECT_URI_AUTH=https://auth.uruenterprises.com/oauth/google/callback
GOOGLE_REDIRECT_URI_INTEGRATIONS=https://integrations.uruenterprises.com/oauth/google/callback

# ===========================================
# COMPOSIO INTEGRATION
# ===========================================
URU_COMPOSIO_API_KEY=your_composio_api_key_here
URU_COMPOSIO_BASE_URL=https://backend.composio.dev/api

# ===========================================
# EXTERNAL INTEGRATIONS
# ===========================================
N8N_SSE_URL=https://n8n-uru-u46170.vm.elestio.app/mcp/uru-workspace-platform/sse
OPENAI_API_KEY=your_openai_api_key_here

# ===========================================
# NEW ARCHITECTURE SERVICE URLS (Production)
# ===========================================

# Frontend Service
FRONTEND_URL=https://app.uruenterprises.com
NEXT_PUBLIC_API_URL=https://app.uruenterprises.com

# Authentication Service
AUTH_SERVICE_URL=https://auth.uruenterprises.com
NEXT_PUBLIC_AUTH_URL=https://auth.uruenterprises.com

# Integration Service
INTEGRATIONS_SERVICE_URL=https://integrations.uruenterprises.com
NEXT_PUBLIC_INTEGRATIONS_URL=https://integrations.uruenterprises.com

# MCP Proxy Service
MCP_PROXY_URL=https://mcp.uruenterprises.com
NEXT_PUBLIC_MCP_URL=https://mcp.uruenterprises.com

# Composio Service (internal)
COMPOSIO_SERVICE_URL=http://composio-service:8001

# ===========================================
# FRONTEND CONFIGURATION
# ===========================================
NEXT_PUBLIC_USE_NEW_ARCHITECTURE=true

# ===========================================
# CORS CONFIGURATION
# ===========================================
CORS_ORIGINS=https://app.uruenterprises.com,https://auth.uruenterprises.com,https://integrations.uruenterprises.com,https://mcp.uruenterprises.com

# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
APP_HOST=0.0.0.0
PORT=3000
HOSTNAME=0.0.0.0

# Service-specific ports (for internal Docker communication)
AUTH_SERVICE_PORT=8003
INTEGRATIONS_SERVICE_PORT=8002
COMPOSIO_SERVICE_PORT=8001
MCP_PROXY_PORT=3001
FRONTEND_PORT=3000
